// 吸隔声模块JavaScript

class SoundInsulationManager {
    constructor() {
        this.selectedVehicles = new Set();
        this.vehicleData = [];
        this.areaData = [];
        this.currentAreaId = null;
        this.chart = null;
        this.currentComparisonData = null;
        
        this.init();
    }
    
    init() {
        this.loadAreas();
        this.bindEvents();
        this.initMultiselect();
    }
    
    async loadAreas() {
        try {
            const response = await request.get('/sound_insulation/api/areas');
            this.areaData = response.data;
            this.populateAreaSelect();
        } catch (error) {
            showMessage('加载区域列表失败: ' + error.message, 'error');
        }
    }
    
    populateAreaSelect() {
        const areaSelect = document.getElementById('area-select');
        areaSelect.innerHTML = '<option value="">请选择区域</option>';
        
        this.areaData.forEach(area => {
            const option = document.createElement('option');
            option.value = area.id;
            option.textContent = area.name;
            areaSelect.appendChild(option);
        });
    }
    
    async loadVehicles(areaId) {
        try {
            const response = await request.get(`/sound_insulation/api/vehicles?area_id=${areaId}`);
            this.vehicleData = response.data;
            this.populateVehicleOptions();
            this.updateMultiselectPlaceholder();
        } catch (error) {
            showMessage('加载车型列表失败: ' + error.message, 'error');
        }
    }
    
    populateVehicleOptions() {
        const optionsContainer = document.querySelector('.multiselect-options');
        optionsContainer.innerHTML = '';
        
        if (this.vehicleData.length === 0) {
            optionsContainer.innerHTML = '<div class="text-center p-3 text-muted">该区域暂无测试数据</div>';
            return;
        }
        
        this.vehicleData.forEach(vehicle => {
            const option = document.createElement('div');
            option.className = 'multiselect-option';
            option.innerHTML = `
                <input type="checkbox" id="vehicle-${vehicle.id}" value="${vehicle.id}">
                <label for="vehicle-${vehicle.id}" class="mb-0">${vehicle.name} (${vehicle.code})</label>
            `;
            
            const checkbox = option.querySelector('input[type="checkbox"]');
            checkbox.addEventListener('change', (e) => {
                this.handleVehicleSelection(vehicle, e.target.checked);
            });
            
            optionsContainer.appendChild(option);
        });
    }
    
    handleVehicleSelection(vehicle, isSelected) {
        if (isSelected) {
            this.selectedVehicles.add(vehicle.id);
        } else {
            this.selectedVehicles.delete(vehicle.id);
        }
        
        this.updateSelectedItems();
        this.updateGenerateButton();
    }
    
    updateSelectedItems() {
        const container = document.querySelector('.selected-items');
        container.innerHTML = '';
        
        this.selectedVehicles.forEach(vehicleId => {
            const vehicle = this.vehicleData.find(v => v.id === vehicleId);
            if (vehicle) {
                const item = document.createElement('div');
                item.className = 'selected-item';
                item.innerHTML = `
                    <span>${vehicle.name}</span>
                    <button type="button" class="remove-btn" data-vehicle-id="${vehicle.id}">×</button>
                `;
                
                const removeBtn = item.querySelector('.remove-btn');
                removeBtn.addEventListener('click', () => {
                    this.removeVehicle(vehicle.id);
                });
                
                container.appendChild(item);
            }
        });
    }
    
    removeVehicle(vehicleId) {
        this.selectedVehicles.delete(vehicleId);
        
        // 更新复选框状态
        const checkbox = document.getElementById(`vehicle-${vehicleId}`);
        if (checkbox) {
            checkbox.checked = false;
        }
        
        this.updateSelectedItems();
        this.updateGenerateButton();
    }
    
    updateMultiselectPlaceholder() {
        const input = document.querySelector('.multiselect-input');
        if (this.vehicleData.length === 0) {
            input.placeholder = '该区域暂无测试数据';
            input.disabled = true;
        } else {
            input.placeholder = '点击选择车型...';
            input.disabled = false;
        }
    }
    
    updateGenerateButton() {
        const btn = document.getElementById('generate-comparison-btn');
        btn.disabled = !this.currentAreaId || this.selectedVehicles.size === 0;
    }
    
    initMultiselect() {
        const container = document.querySelector('.multiselect-container');
        const input = document.querySelector('.multiselect-input');
        const dropdown = document.querySelector('.multiselect-dropdown');
        const searchInput = document.querySelector('.multiselect-search input');
        
        // 点击输入框切换下拉菜单
        input.addEventListener('click', (e) => {
            e.preventDefault();
            if (!input.disabled) {
                container.classList.toggle('open');
            }
        });
        
        // 搜索功能
        searchInput.addEventListener('input', (e) => {
            this.filterVehicleOptions(e.target.value);
        });
        
        // 点击外部关闭下拉菜单
        document.addEventListener('click', (e) => {
            if (!container.contains(e.target)) {
                container.classList.remove('open');
            }
        });
    }
    
    filterVehicleOptions(searchTerm) {
        const options = document.querySelectorAll('.multiselect-option');
        const term = searchTerm.toLowerCase();
        
        options.forEach(option => {
            const label = option.querySelector('label');
            if (label) {
                const text = label.textContent.toLowerCase();
                option.style.display = text.includes(term) ? 'flex' : 'none';
            }
        });
    }
    
    bindEvents() {
        // 区域选择事件
        document.getElementById('area-select').addEventListener('change', (e) => {
            const areaId = e.target.value;
            this.currentAreaId = areaId ? parseInt(areaId) : null;
            
            // 清空已选择的车型
            this.selectedVehicles.clear();
            this.updateSelectedItems();
            
            if (areaId) {
                this.loadVehicles(areaId);
            } else {
                this.vehicleData = [];
                this.populateVehicleOptions();
                this.updateMultiselectPlaceholder();
            }
            
            this.updateGenerateButton();
        });
        
        // 生成对比表按钮
        document.getElementById('generate-comparison-btn').addEventListener('click', () => {
            this.generateComparison();
        });
        
        // 导出按钮
        document.getElementById('export-btn').addEventListener('click', () => {
            this.exportData();
        });
    }
    
    async generateComparison() {
        if (!this.currentAreaId || this.selectedVehicles.size === 0) {
            showMessage('请选择区域和车型', 'warning');
            return;
        }
        
        try {
            this.showLoading(true);
            
            const response = await request.post('/sound_insulation/api/comparison', {
                area_id: this.currentAreaId,
                vehicle_ids: Array.from(this.selectedVehicles)
            });
            
            this.currentComparisonData = response.data;
            this.displayComparisonResults(response.data);
            
        } catch (error) {
            showMessage('生成对比数据失败: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }
    
    displayComparisonResults(data) {
        // 更新标题
        document.getElementById('area-title').textContent = `${data.area_info.name}隔声量（ATF）对比`;
        document.getElementById('vehicle-count').textContent = `${data.vehicle_info.length} 个车型`;
        
        // 显示结果区域
        document.getElementById('empty-state').style.display = 'none';
        document.getElementById('results-card').style.display = 'block';
        
        // 生成表格
        this.generateTable(data);
        
        // 生成图表
        this.generateChart(data);
        
        // 生成测试信息表
        this.generateTestInfoTable(data);
    }
    
    generateTable(data) {
        const table = document.getElementById('comparison-table');
        const thead = table.querySelector('thead');
        const tbody = table.querySelector('tbody');
        
        // 生成表头
        let headerHtml = '<tr><th>中心频率(Hz)</th>';
        data.vehicle_info.forEach(vehicle => {
            headerHtml += `<th>${vehicle.name} (dB)</th>`;
        });
        headerHtml += '</tr>';
        thead.innerHTML = headerHtml;
        
        // 生成表格数据
        let bodyHtml = '';
        data.table_data.forEach(row => {
            bodyHtml += `<tr><td>${row.frequency}</td>`;
            data.vehicle_info.forEach(vehicle => {
                const value = row[`vehicle_${vehicle.id}`];
                bodyHtml += `<td>${value !== null && value !== undefined ? value.toFixed(1) : '-'}</td>`;
            });
            bodyHtml += '</tr>';
        });
        tbody.innerHTML = bodyHtml;
    }
    
    generateChart(data) {
        const container = document.getElementById('chart-container');
        
        if (this.chart) {
            this.chart.dispose();
        }
        
        this.chart = echarts.init(container);
        
        const option = {
            title: {
                text: `${data.area_info.name}隔声量对比`,
                left: 'center',
                textStyle: {
                    fontSize: 16,
                    fontWeight: 'bold'
                }
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross'
                },
                formatter: function(params) {
                    let html = `<div><strong>频率: ${params[0].axisValue} Hz</strong></div>`;
                    params.forEach(param => {
                        if (param.value !== null) {
                            html += `<div>${param.marker} ${param.seriesName}: ${param.value.toFixed(1)} dB</div>`;
                        }
                    });
                    return html;
                }
            },
            legend: {
                data: data.chart_data.series.map(s => s.name),
                top: 30,
                type: 'scroll'
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                top: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: data.chart_data.frequencies,
                name: '频率 (Hz)',
                nameLocation: 'middle',
                nameGap: 30,
                axisLabel: {
                    rotate: 45
                }
            },
            yAxis: {
                type: 'value',
                name: '隔声量 (dB)',
                nameLocation: 'middle',
                nameGap: 50
            },
            series: data.chart_data.series.map((series, index) => ({
                name: series.name,
                type: 'line',
                data: series.data,
                smooth: true,
                symbol: 'circle',
                symbolSize: 6,
                lineStyle: {
                    width: 2
                },
                emphasis: {
                    focus: 'series'
                }
            }))
        };
        
        this.chart.setOption(option);
        
        // 添加点击事件
        this.chart.on('click', (params) => {
            if (params.componentType === 'series') {
                const vehicleInfo = data.vehicle_info.find(v => v.name === params.seriesName);
                if (vehicleInfo) {
                    this.showTestImage(vehicleInfo.id, data.area_info.id, vehicleInfo.name, data.area_info.name);
                }
            }
        });
        
        // 响应式
        window.addEventListener('resize', () => {
            if (this.chart) {
                this.chart.resize();
            }
        });
    }
    
    generateTestInfoTable(data) {
        const tbody = document.querySelector('#test-info-table tbody');
        let html = '';
        
        data.vehicle_info.forEach(vehicle => {
            html += `
                <tr>
                    <td>${vehicle.name}</td>
                    <td>${vehicle.test_date || '-'}</td>
                    <td>${vehicle.test_engineer || '-'}</td>
                    <td>${vehicle.test_location || '-'}</td>
                    <td>
                        <button type="button" class="btn btn-outline-primary btn-sm btn-view-image" 
                                data-vehicle-id="${vehicle.id}" data-area-id="${data.area_info.id}"
                                data-vehicle-name="${vehicle.name}" data-area-name="${data.area_info.name}">
                            <i class="fas fa-image me-1"></i>查看附图
                        </button>
                    </td>
                </tr>
            `;
        });
        
        tbody.innerHTML = html;
        
        // 绑定查看附图按钮事件
        document.querySelectorAll('.btn-view-image').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const vehicleId = e.target.closest('button').dataset.vehicleId;
                const areaId = e.target.closest('button').dataset.areaId;
                const vehicleName = e.target.closest('button').dataset.vehicleName;
                const areaName = e.target.closest('button').dataset.areaName;
                this.showTestImage(vehicleId, areaId, vehicleName, areaName);
            });
        });
    }
    
    async showTestImage(vehicleId, areaId, vehicleName, areaName) {
        try {
            const response = await request.get(`/sound_insulation/api/test_image?vehicle_id=${vehicleId}&area_id=${areaId}`);
            const imageInfo = response.data;
            
            // 更新模态框标题
            document.getElementById('imageModalTitle').textContent = `${areaName} - ${vehicleName} - 测试附图`;
            
            // 更新图片
            const img = document.getElementById('test-image');
            if (imageInfo.test_image_path) {
                img.src = imageInfo.test_image_path;
                img.style.display = 'block';
            } else {
                img.style.display = 'none';
                document.getElementById('image-container').innerHTML = '<div class="text-muted">暂无测试图片</div>';
            }
            
            // 更新图片信息
            document.getElementById('image-info').innerHTML = `
                <div class="row">
                    <div class="col-sm-3"><strong>车型:</strong></div>
                    <div class="col-sm-9">${imageInfo.vehicle_name}</div>
                </div>
                <div class="row">
                    <div class="col-sm-3"><strong>测试区域:</strong></div>
                    <div class="col-sm-9">${imageInfo.area_name}</div>
                </div>
                <div class="row">
                    <div class="col-sm-3"><strong>测试日期:</strong></div>
                    <div class="col-sm-9">${imageInfo.test_date || '-'}</div>
                </div>
                <div class="row">
                    <div class="col-sm-3"><strong>测试工程师:</strong></div>
                    <div class="col-sm-9">${imageInfo.test_engineer || '-'}</div>
                </div>
                <div class="row">
                    <div class="col-sm-3"><strong>测试地点:</strong></div>
                    <div class="col-sm-9">${imageInfo.test_location || '-'}</div>
                </div>
                ${imageInfo.remarks ? `
                <div class="row">
                    <div class="col-sm-3"><strong>备注:</strong></div>
                    <div class="col-sm-9">${imageInfo.remarks}</div>
                </div>
                ` : ''}
            `;
            
            // 设置下载按钮
            const downloadBtn = document.getElementById('download-image-btn');
            if (imageInfo.test_image_path) {
                downloadBtn.style.display = 'inline-block';
                downloadBtn.onclick = () => {
                    window.open(imageInfo.test_image_path, '_blank');
                };
            } else {
                downloadBtn.style.display = 'none';
            }
            
            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('imageModal'));
            modal.show();
            
        } catch (error) {
            showMessage('获取测试图片失败: ' + error.message, 'error');
        }
    }
    
    async exportData() {
        if (!this.currentComparisonData) {
            showMessage('请先生成对比数据', 'warning');
            return;
        }
        
        try {
            const response = await fetch('/sound_insulation/api/export', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    area_id: this.currentAreaId,
                    vehicle_ids: Array.from(this.selectedVehicles)
                })
            });
            
            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'sound_insulation_comparison.csv';
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
                
                showMessage('数据导出成功', 'success');
            } else {
                throw new Error('导出失败');
            }
        } catch (error) {
            showMessage('导出数据失败: ' + error.message, 'error');
        }
    }
    
    showLoading(show) {
        document.getElementById('loading-indicator').style.display = show ? 'block' : 'none';
        document.getElementById('results-card').style.display = show ? 'none' : (this.currentComparisonData ? 'block' : 'none');
        document.getElementById('empty-state').style.display = show ? 'none' : (this.currentComparisonData ? 'none' : 'block');
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    window.soundInsulationManager = new SoundInsulationManager();
});
